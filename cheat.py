import pymem
import pymem.process
import time
import dearpygui.dearpygui as dpg
import threading

# --- Config ---
PROCESS_NAME = "TheSpellBrigade.exe"
GOLD_BASE_OFFSET = 0x3858F00
LEVEL_BASE_OFFSET = 0x03921E48
HP_BASE_OFFSET = 0x1C40600
GOLD_OFFSETS = [0x20, 0xC0, 0x10, 0xB8, 0x0, 0x38]
LEVEL_OFFSETS = [0xB8, 0x0, 0x10, 0x28, 0xA8]
HP_OFFSETS = [0x28, 0x18, 0x80, 0x20, 0x40, 0x28, 0x1E0, 0x60]
# --------------

# Global state dictionary
app_state = {
    "pm": None,
    "module_base": 0,
    "unity_module_base": 0,
    "game_connected": False,
    "gold_address": 0,
    "level_address": 0,
    "hp_address": 0,
    "status_text": "Please start the game, then click 'Connect to Game'.",
    "current_gold": "N/A",
    "current_level": "N/A",
    "current_hp": "N/A",
    "gold_locked": False,
    "level_locked": False,
    "hp_locked": False,
    "debug_printed": False
}

def find_pointer_address(pm, base, offsets, debug_name=""):
    """
    Calculates the final address from a base address and multi-level offsets (64-bit).
    Optionally prints debug information.
    """
    try:
        addr = pm.read_longlong(base)
        if debug_name: print(f"[{debug_name}] Base Address: {hex(base)} -> Initial Addr: {hex(addr)}")
        if addr == 0:
            if debug_name: print(f"[{debug_name}] Address became null at base.")
            return None
        
        for i, offset in enumerate(offsets[:-1]):
            new_addr = pm.read_longlong(addr + offset)
            if debug_name: print(f"[{debug_name}] Step {i}: Addr: {hex(addr)} + Offset: {hex(offset)} -> New Addr: {hex(new_addr)}")
            addr = new_addr
            if addr == 0:
                if debug_name: print(f"[{debug_name}] Address became null at offset step {i}.")
                return None
            
        final_addr = addr + offsets[-1]
        if debug_name: print(f"[{debug_name}] Final Step: Addr: {hex(addr)} + Last Offset: {hex(offsets[-1])} -> Final Addr: {hex(final_addr)}")
        return final_addr
    except Exception as e:
        if debug_name: print(f"[{debug_name}] Exception during pointer calculation: {e}")
        return None

def connect_to_game():
    """
    Runs in a background thread to connect to the game process.
    """
    app_state["game_connected"] = False
    app_state["status_text"] = f"Searching for game process: {PROCESS_NAME}..."
    
    try:
        app_state["pm"] = pymem.Pymem(PROCESS_NAME)
        app_state["module_base"] = pymem.process.module_from_name(
            app_state["pm"].process_handle, "GameAssembly.dll"
        ).lpBaseOfDll
        app_state["unity_module_base"] = pymem.process.module_from_name(
            app_state["pm"].process_handle, "UnityPlayer.dll"
        ).lpBaseOfDll
        
        app_state["game_connected"] = True
        app_state["status_text"] = f"Successfully connected! Process ID: {app_state['pm'].process_id}"
        app_state["debug_printed"] = False  # Reset debug flag on new connection
        print(f"[+] GameAssembly.dll module base: {hex(app_state['module_base'])}")
        print(f"[+] UnityPlayer.dll module base: {hex(app_state['unity_module_base'])}")

    except pymem.exception.ProcessNotFound:
        app_state["status_text"] = f"Process '{PROCESS_NAME}' not found. Please start the game first."
    except Exception as e:
        app_state["status_text"] = f"Error during connection: {e}"

def start_connection_thread():
    """Starts the connection thread."""
    threading.Thread(target=connect_to_game, daemon=True).start()

def set_gold_value():
    """
    Callback for the 'Set Gold' button.
    """
    if not app_state["game_connected"] or not app_state["gold_address"]:
        app_state["status_text"] = "Please connect to the game and ensure gold address is valid."
        return

    try:
        new_gold = dpg.get_value("new_gold_input")
        app_state["pm"].write_int(app_state["gold_address"], new_gold)
        
        # Verify write
        time.sleep(0.1)
        written_gold = app_state["pm"].read_int(app_state["gold_address"])
        if written_gold == new_gold:
            app_state["status_text"] = f"Success! Gold has been set to: {new_gold}"
            app_state["current_gold"] = str(new_gold)
        else:
            app_state["status_text"] = "Write operation failed!"
            
    except Exception as e:
        app_state["status_text"] = f"Error setting gold: {e}"

def set_level_value():
    """
    Callback for the 'Set Level' button.
    """
    if not app_state["game_connected"] or not app_state["level_address"]:
        app_state["status_text"] = "Please connect to the game and ensure level address is valid."
        return

    try:
        new_level = dpg.get_value("new_level_input")
        app_state["pm"].write_int(app_state["level_address"], new_level)
        
        # Verify write
        time.sleep(0.1)
        written_level = app_state["pm"].read_int(app_state["level_address"])
        if written_level == new_level:
            app_state["status_text"] = f"Success! Level has been set to: {new_level}"
            app_state["current_level"] = str(new_level)
        else:
            app_state["status_text"] = "Write operation failed!"
            
    except Exception as e:
        app_state["status_text"] = f"Error setting level: {e}"

def set_hp_value():
    """
    Callback for the 'Set HP' button.
    """
    if not app_state["game_connected"] or not app_state["hp_address"]:
        app_state["status_text"] = "Please connect to the game and ensure HP address is valid."
        return

    try:
        new_hp = float(dpg.get_value("new_hp_input"))
        print(f"[DEBUG] Attempting to write {new_hp} to HP address: {hex(app_state['hp_address'])}")
        app_state["pm"].write_float(app_state["hp_address"], new_hp)
        
        # Verify write
        time.sleep(0.1)
        written_hp = app_state["pm"].read_float(app_state["hp_address"])
        print(f"[DEBUG] Read back value: {written_hp}")
        if abs(written_hp - new_hp) < 0.001:  # Float comparison with tolerance
            app_state["status_text"] = f"Success! HP has been set to: {new_hp:.1f}"
            app_state["current_hp"] = f"{new_hp:.1f}"
        else:
            app_state["status_text"] = f"Write operation failed! Expected: {new_hp}, Got: {written_hp}"
            
    except Exception as e:
        app_state["status_text"] = f"Error setting HP: {e}"
        print(f"[DEBUG] Exception in set_hp_value: {e}")

def test_direct_hp_address():
    """
    Test function to directly access the known HP address for debugging.
    """
    if not app_state["game_connected"]:
        app_state["status_text"] = "Please connect to the game first."
        return
    
    try:
        # Test the expected address: 1BC3501BBE0
        test_address = 0x1BC3501BBE0
        print(f"[DEBUG] Testing direct HP address: {hex(test_address)}")
        
        # Try to read from the expected address
        hp_value = app_state["pm"].read_float(test_address)
        print(f"[DEBUG] HP value at {hex(test_address)}: {hp_value}")
        app_state["status_text"] = f"Direct read test: HP = {hp_value:.1f} at {hex(test_address)}"
        
    except Exception as e:
        print(f"[DEBUG] Error reading from direct address: {e}")
        app_state["status_text"] = f"Error reading direct address: {e}"

def create_gui():
    """
    Creates the Dear PyGui interface.
    """
    dpg.create_context()

    with dpg.window(label="The Spell Brigade Cheat by Roo", width=500, height=280, no_resize=True, no_title_bar=True, no_move=True, no_collapse=True):
        dpg.add_text("", tag="status_label")
        dpg.add_separator()
        
        with dpg.group(horizontal=True):
            dpg.add_button(label="Connect to Game", callback=start_connection_thread, width=-1)

        dpg.add_separator()

        # Table for cheats
        with dpg.table(header_row=True, borders_innerH=True, borders_innerV=True):
            dpg.add_table_column(label="Cheat")
            dpg.add_table_column(label="Current Value")
            dpg.add_table_column(label="Set Value")
            dpg.add_table_column(label="Action")
            dpg.add_table_column(label="Lock")

            # Gold Row
            with dpg.table_row():
                dpg.add_text("Gold")
                dpg.add_text("N/A", tag="current_gold_label")
                dpg.add_input_int(tag="new_gold_input", default_value=99999, width=100)
                dpg.add_button(label="Set", callback=set_gold_value, width=-1)
                dpg.add_checkbox(tag="gold_lock_checkbox", callback=lambda: app_state.update({"gold_locked": dpg.get_value("gold_lock_checkbox")}))

            # Level Row
            with dpg.table_row():
                dpg.add_text("Level")
                dpg.add_text("N/A", tag="current_level_label")
                dpg.add_input_int(tag="new_level_input", default_value=100, width=100)
                dpg.add_button(label="Set", callback=set_level_value, width=-1)
                dpg.add_checkbox(tag="level_lock_checkbox", callback=lambda: app_state.update({"level_locked": dpg.get_value("level_lock_checkbox")}))

            # HP Row
            with dpg.table_row():
                dpg.add_text("HP")
                dpg.add_text("N/A", tag="current_hp_label")
                dpg.add_input_float(tag="new_hp_input", default_value=999.0, width=100, format="%.1f")
                dpg.add_button(label="Set", callback=set_hp_value, width=-1)
                dpg.add_checkbox(tag="hp_lock_checkbox", callback=lambda: app_state.update({"hp_locked": dpg.get_value("hp_lock_checkbox")}))

        dpg.add_spacer(height=10)
        dpg.add_text("Hint: Please run this program as an administrator.", color=(255, 255, 0))

    dpg.create_viewport(title='The Spell Brigade Cheat', width=500, height=280)
    dpg.setup_dearpygui()
    dpg.show_viewport()

def main_loop():
    """
    Main render loop to update the UI.
    """
    while dpg.is_dearpygui_running():
        # Update UI elements
        dpg.set_value("status_label", app_state["status_text"])
        
        if app_state["game_connected"]:
            # Determine if this is the first run for debugging
            is_debug_run = not app_state["debug_printed"]

            # Calculate gold address
            gold_addr = find_pointer_address(
                app_state["pm"],
                app_state["module_base"] + GOLD_BASE_OFFSET,
                GOLD_OFFSETS,
                "GOLD" if is_debug_run else ""
            )
            app_state["gold_address"] = gold_addr

            if gold_addr:
                try:
                    if app_state["gold_locked"]:
                        locked_gold_value = dpg.get_value("new_gold_input")
                        app_state["pm"].write_int(gold_addr, locked_gold_value)
                        app_state["current_gold"] = str(locked_gold_value) + " (Locked)"
                    else:
                        current_gold = app_state["pm"].read_int(gold_addr)
                        app_state["current_gold"] = str(current_gold)
                except Exception:
                    app_state["current_gold"] = "Read/Write Error"
            else:
                app_state["current_gold"] = "Invalid Address"
            
            # Calculate level address
            level_addr = find_pointer_address(
                app_state["pm"],
                app_state["module_base"] + LEVEL_BASE_OFFSET,
                LEVEL_OFFSETS,
                "LEVEL" if is_debug_run else ""
            )
            app_state["level_address"] = level_addr

            if level_addr:
                try:
                    if app_state["level_locked"]:
                        locked_level_value = dpg.get_value("new_level_input")
                        app_state["pm"].write_int(level_addr, locked_level_value)
                        app_state["current_level"] = str(locked_level_value) + " (Locked)"
                    else:
                        current_level = app_state["pm"].read_int(level_addr)
                        app_state["current_level"] = str(current_level)
                except Exception:
                    app_state["current_level"] = "Read/Write Error"
            else:
                app_state["current_level"] = "Invalid Address"
            
            # Calculate HP address
            hp_addr = find_pointer_address(
                app_state["pm"],
                app_state["unity_module_base"] + HP_BASE_OFFSET,
                HP_OFFSETS,
                "HP" if is_debug_run else ""
            )
            app_state["hp_address"] = hp_addr

            if hp_addr:
                try:
                    if app_state["hp_locked"]:
                        locked_hp_value = float(dpg.get_value("new_hp_input"))
                        app_state["pm"].write_float(hp_addr, locked_hp_value)
                        app_state["current_hp"] = f"{locked_hp_value:.1f} (Locked)"
                    else:
                        current_hp = app_state["pm"].read_float(hp_addr)
                        app_state["current_hp"] = f"{current_hp:.1f}"
                except Exception:
                    app_state["current_hp"] = "Read/Write Error"
            else:
                app_state["current_hp"] = "Invalid Address"

            # Ensure debug info is only printed once per connection
            if is_debug_run:
                app_state["debug_printed"] = True

        dpg.set_value("current_gold_label", app_state["current_gold"])
        dpg.set_value("current_level_label", app_state["current_level"])
        dpg.set_value("current_hp_label", app_state["current_hp"])
        
        dpg.render_dearpygui_frame()
        time.sleep(0.05) # Reduce CPU usage

    dpg.destroy_context()

if __name__ == "__main__":
    create_gui()
    main_loop()
